"use client";

import { Attachment, ToolInvocation } from "ai";
import { motion } from "framer-motion";
import { ReactNode } from "react";

import { BotIcon, UserIcon } from "./icons";
import { Markdown } from "./markdown";
import { PreviewAttachment } from "./preview-attachment";
import { Chart } from "./chart";
import { ThinkingCard } from "./thinking-card";

// 生成随机图表数据的函数
const generateRandomChartData = () => {
  const chartTypes = ['bar', 'pie'] as const;
  const type = chartTypes[Math.floor(Math.random() * chartTypes.length)];

  const sampleData = [
    {
      labels: ["苹果", "香蕉", "橙子", "葡萄", "草莓"],
      data: [30, 25, 20, 15, 10],
      title: "水果销量分布"
    },
    {
      labels: ["北京", "上海", "广州", "深圳"],
      data: [45, 35, 25, 20],
      title: "城市人口分布"
    },
    {
      labels: ["JavaScript", "Python", "Java", "C++", "Go"],
      data: [40, 30, 20, 15, 10],
      title: "编程语言使用率"
    },
    {
      labels: ["Q1", "Q2", "Q3", "Q4"],
      data: [120, 150, 180, 200],
      title: "季度销售额"
    },
    {
      labels: ["移动端", "桌面端", "平板端"],
      data: [60, 30, 10],
      title: "设备使用分布"
    }
  ];

  const randomData = sampleData[Math.floor(Math.random() * sampleData.length)];

  return {
    ...randomData,
    type,
    data: randomData.data.map(val => val + Math.floor(Math.random() * 20) - 10) // 添加随机变化
  };
};

export const Message = ({
  chatId,
  role,
  name,
  content,
  isLoading,
  toolInvocations,
  attachments,
}: {
  chatId: string;
  role: string;
  name: string;
  content: string | ReactNode;
  isLoading: boolean;
  toolInvocations: Array<ToolInvocation> | undefined;
  attachments?: Array<Attachment>;
}) => {
  // 如果 role 为 "think"，渲染 ThinkingCard 组件
  if (role === "think") {
    return (
      <ThinkingCard
        isVisible={true}
        content={typeof content === "string" ? content : ""}
        isLoading={isLoading}
      />
    );
  }

  return (
    <motion.div
      className={`flex px-4 w-full md:w-[500px] first-of-type:pt-20 ${
        role === "user" ? "justify-end" : "justify-start"
      }`}
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
    >
      <div className={`flex flex-col gap-2 max-w-[80%] md:max-w-[500px] ${
        role === "user"
          ? "bg-blue-50 dark:bg-blue-900/20 rounded-2xl px-4 py-3 ml-auto"
          : "w-full"
      }`}>
        {content && typeof content === "string" && (
          <div className={`flex flex-col gap-4 ${
            role === "user"
              ? "text-blue-900 dark:text-blue-100"
              : "text-zinc-800 dark:text-zinc-300"
          }`}>
            <Markdown>{content}</Markdown>
          </div>
        )}

        {/* 为助手消息自动添加随机图表 */}
        {role === "tool" && name === "chart" && (
          <div className="flex flex-col gap-4">
            <div>
              {(() => {
                // 检查吐字效果是否结束，通过判断content是否为完整的JSON字符串
                try {
                  const parsedContent = JSON.parse(content as string);
                  // 如果能成功解析且包含必要字段，说明吐字效果已结束
                  if (parsedContent.chart_data && parsedContent.chart_type) {
                    return (
                      <Chart chartData={{
                        labels: parsedContent.chart_data.labels,
                        data: parsedContent.chart_data.data,
                        type: parsedContent.chart_type,
                        title: parsedContent.chart_data.title,
                      }} />
                    );
                  }
                } catch (error) {
                  // JSON解析失败，说明吐字效果还在进行中
                  console.log('Chart data still loading...');
                }

                // 吐字效果进行中，显示加载状态
                return <Chart />;
              })()}
            </div>
          </div>
        )}

        {toolInvocations && (
          <div className="flex flex-col gap-4">
            {toolInvocations.map((toolInvocation) => {
              const { toolName, toolCallId, state } = toolInvocation;

              if (state === "result") {
                const { result } = toolInvocation;

                return (
                  <div key={toolCallId}>
                    {toolName === "chart" ? (
                      <Chart chartData={result} />
                    ) : null}
                  </div>
                );
              } else {
                return (
                  <div key={toolCallId} className="skeleton">
                    {toolName === "chart" ? (
                      <Chart />
                    ) : null}
                  </div>
                );
              }
            })}
          </div>
        )}

        {attachments && (
          <div className="flex flex-row gap-2">
            {attachments.map((attachment) => (
              <PreviewAttachment key={attachment.url} attachment={attachment} />
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
};
